#!/usr/bin/env python3
"""
批量修复容器带宽限制脚本
修复由于单位转换错误导致的带宽限制问题
"""

import json
import subprocess
import sys
import re

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def get_all_containers():
    """获取所有容器列表"""
    success, stdout, stderr = run_command("lxc list --format json")
    if not success:
        print(f"获取容器列表失败: {stderr}")
        return []
    
    try:
        containers = json.loads(stdout)
        return [c['name'] for c in containers]
    except json.JSONDecodeError as e:
        print(f"解析容器列表失败: {e}")
        return []

def get_container_network_limits(container_name):
    """获取容器的网络限制配置"""
    success, stdout, stderr = run_command(f"lxc config show {container_name} --format json")
    if not success:
        print(f"获取容器 {container_name} 配置失败: {stderr}")
        return None, None
    
    try:
        config = json.loads(stdout)
        devices = config.get('devices', {})
        eth0 = devices.get('eth0', {})
        
        ingress = eth0.get('limits.ingress')
        egress = eth0.get('limits.egress')
        
        return ingress, egress
    except json.JSONDecodeError as e:
        print(f"解析容器 {container_name} 配置失败: {e}")
        return None, None

def fix_container_bandwidth(container_name, ingress, egress):
    """修复单个容器的带宽限制"""
    if not ingress or not egress:
        return False, "没有找到网络限制配置"
    
    try:
        # 检查是否是错误的125000倍数配置
        ingress_val = int(ingress)
        egress_val = int(egress)
        
        # 如果是125000的倍数，说明使用了错误的转换系数
        if ingress_val % 125000 == 0 and egress_val % 125000 == 0:
            # 计算原始Mbps值
            original_ingress_mbps = ingress_val // 125000
            original_egress_mbps = egress_val // 125000
            
            # 计算正确的bit/s值
            correct_ingress = original_ingress_mbps * 1000000
            correct_egress = original_egress_mbps * 1000000
            
            print(f"容器 {container_name}:")
            print(f"  当前配置: ingress={ingress_val} bit/s ({original_ingress_mbps} Mbps), egress={egress_val} bit/s ({original_egress_mbps} Mbps)")
            print(f"  修复为:   ingress={correct_ingress} bit/s ({original_ingress_mbps} Mbps), egress={correct_egress} bit/s ({original_egress_mbps} Mbps)")
            
            # 应用修复
            cmd1 = f"lxc config device set {container_name} eth0 limits.ingress={correct_ingress}"
            cmd2 = f"lxc config device set {container_name} eth0 limits.egress={correct_egress}"
            
            success1, _, stderr1 = run_command(cmd1)
            success2, _, stderr2 = run_command(cmd2)
            
            if success1 and success2:
                print(f"  ✅ 修复成功")
                return True, "修复成功"
            else:
                error_msg = f"修复失败: {stderr1} {stderr2}"
                print(f"  ❌ {error_msg}")
                return False, error_msg
        else:
            print(f"容器 {container_name}: 配置正常，无需修复")
            return True, "配置正常"
            
    except ValueError as e:
        error_msg = f"解析带宽值失败: {e}"
        print(f"容器 {container_name}: ❌ {error_msg}")
        return False, error_msg

def main():
    print("=== 批量修复容器带宽限制脚本 ===")
    print("此脚本将修复由于单位转换错误导致的带宽限制问题")
    print()
    
    # 获取所有容器
    containers = get_all_containers()
    if not containers:
        print("没有找到任何容器")
        return
    
    print(f"找到 {len(containers)} 个容器")
    print()
    
    # 询问是否继续
    response = input("是否继续修复所有容器的带宽限制？(y/N): ").strip().lower()
    if response != 'y':
        print("操作已取消")
        return
    
    print()
    print("开始修复...")
    print("=" * 50)
    
    success_count = 0
    error_count = 0
    skip_count = 0
    
    for container in containers:
        ingress, egress = get_container_network_limits(container)
        
        if ingress is None or egress is None:
            print(f"容器 {container}: ⚠️  跳过（无网络限制配置）")
            skip_count += 1
            continue
        
        success, message = fix_container_bandwidth(container, ingress, egress)
        
        if success:
            if "修复成功" in message:
                success_count += 1
            else:
                skip_count += 1
        else:
            error_count += 1
        
        print()
    
    print("=" * 50)
    print("修复完成！")
    print(f"成功修复: {success_count} 个容器")
    print(f"跳过: {skip_count} 个容器")
    print(f"失败: {error_count} 个容器")
    
    if success_count > 0:
        print()
        print("建议重启修复过的容器以确保配置生效：")
        print("lxc restart <容器名>")

if __name__ == "__main__":
    main()
