#!/usr/bin/env python3
"""
批量修复容器带宽限制脚本
修复由于单位转换错误导致的带宽限制问题

安全特性：
1. 只修复明确使用错误转换系数(125000)的容器
2. 提供详细的预览和确认机制
3. 支持备份和回滚
4. 逐个容器确认修复
"""

import json
import subprocess
import sys
import re
import os
import time
from datetime import datetime

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def get_all_containers():
    """获取所有容器列表"""
    # 尝试新版本的命令
    success, stdout, stderr = run_command("lxc list --format json")
    if success:
        try:
            containers = json.loads(stdout)
            return [c['name'] for c in containers]
        except json.JSONDecodeError as e:
            print(f"解析容器列表失败: {e}")
            return []

    # 如果新版本命令失败，尝试旧版本命令
    print("尝试使用兼容模式获取容器列表...")
    success, stdout, stderr = run_command("lxc list -c n --format csv")
    if success:
        # CSV格式解析
        containers = []
        for line in stdout.strip().split('\n'):
            if line.strip() and line.strip() != 'NAME':
                containers.append(line.strip())
        return containers

    # 如果CSV也不支持，使用最基本的命令
    success, stdout, stderr = run_command("lxc list")
    if success:
        containers = []
        lines = stdout.strip().split('\n')
        for line in lines:
            # 解析表格格式的输出
            if '|' in line and not line.startswith('+') and 'NAME' not in line:
                parts = [p.strip() for p in line.split('|')]
                if len(parts) >= 2 and parts[1]:  # 第二列是容器名
                    containers.append(parts[1])
        return containers

    print(f"获取容器列表失败: {stderr}")
    return []

def get_container_network_limits(container_name):
    """获取容器的网络限制配置"""
    # 尝试新版本的JSON格式命令
    success, stdout, stderr = run_command(f"lxc config show {container_name} --format json")
    if success:
        try:
            config = json.loads(stdout)
            devices = config.get('devices', {})
            eth0 = devices.get('eth0', {})

            ingress = eth0.get('limits.ingress')
            egress = eth0.get('limits.egress')

            return ingress, egress, config
        except json.JSONDecodeError as e:
            print(f"解析容器 {container_name} JSON配置失败: {e}")

    # 如果JSON格式失败，使用YAML格式
    success, stdout, stderr = run_command(f"lxc config show {container_name}")
    if not success:
        print(f"获取容器 {container_name} 配置失败: {stderr}")
        return None, None, None

    # 解析YAML格式的输出
    try:
        import yaml
        config = yaml.safe_load(stdout)
        devices = config.get('devices', {})
        eth0 = devices.get('eth0', {})

        ingress = eth0.get('limits.ingress')
        egress = eth0.get('limits.egress')

        return ingress, egress, config
    except ImportError:
        # 如果没有yaml模块，手动解析
        return parse_config_manually(container_name, stdout)
    except Exception as e:
        print(f"解析容器 {container_name} YAML配置失败: {e}")
        return parse_config_manually(container_name, stdout)

def parse_config_manually(container_name, config_text):
    """手动解析lxc config show的输出"""
    try:
        ingress = None
        egress = None
        in_eth0_section = False

        lines = config_text.split('\n')
        for i, line in enumerate(lines):
            line = line.strip()

            # 检查是否进入eth0设备配置段
            if line == 'eth0:' or line.startswith('eth0:'):
                in_eth0_section = True
                continue

            # 检查是否离开eth0段（遇到其他设备或段）
            if in_eth0_section and line.endswith(':') and not line.startswith(' '):
                in_eth0_section = False
                continue

            # 在eth0段内查找limits配置
            if in_eth0_section:
                if 'limits.ingress:' in line:
                    ingress = line.split('limits.ingress:')[1].strip()
                elif 'limits.egress:' in line:
                    egress = line.split('limits.egress:')[1].strip()

        # 创建简化的配置对象用于备份
        config = {
            'devices': {
                'eth0': {}
            }
        }
        if ingress:
            config['devices']['eth0']['limits.ingress'] = ingress
        if egress:
            config['devices']['eth0']['limits.egress'] = egress

        return ingress, egress, config

    except Exception as e:
        print(f"手动解析容器 {container_name} 配置失败: {e}")
        return None, None, None

def backup_container_config(container_name, config):
    """备份容器配置"""
    backup_dir = "container_backups"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"{backup_dir}/{container_name}_{timestamp}.json"

    try:
        with open(backup_file, 'w') as f:
            json.dump(config, f, indent=2)
        return backup_file
    except Exception as e:
        print(f"备份容器 {container_name} 配置失败: {e}")
        return None

def is_safe_to_modify(container_name, ingress_val, egress_val):
    """检查是否安全修改容器配置"""
    # 检查1: 必须是125000的倍数
    if ingress_val % 125000 != 0 or egress_val % 125000 != 0:
        return False, "不是125000的倍数，可能不是错误配置"

    # 检查2: 计算的Mbps值必须是合理的（1-10000 Mbps）
    ingress_mbps = ingress_val // 125000
    egress_mbps = egress_val // 125000

    if not (1 <= ingress_mbps <= 10000) or not (1 <= egress_mbps <= 10000):
        return False, f"计算出的带宽值不合理: {ingress_mbps}/{egress_mbps} Mbps"

    # 检查3: 容器必须存在且可访问
    success, _, _ = run_command(f"lxc info {container_name}")
    if not success:
        return False, "容器不存在或无法访问"

    return True, "安全检查通过"

def analyze_container_bandwidth(container_name, ingress, egress, config):
    """分析容器带宽配置，返回修复建议"""
    if not ingress or not egress:
        return None

    try:
        ingress_val = int(ingress)
        egress_val = int(egress)

        # 安全检查
        safe, reason = is_safe_to_modify(container_name, ingress_val, egress_val)
        if not safe:
            return {
                'container': container_name,
                'status': 'skip',
                'reason': reason,
                'current_ingress': ingress_val,
                'current_egress': egress_val
            }

        # 计算修复值
        original_ingress_mbps = ingress_val // 125000
        original_egress_mbps = egress_val // 125000
        correct_ingress = original_ingress_mbps * 1000000
        correct_egress = original_egress_mbps * 1000000

        return {
            'container': container_name,
            'status': 'need_fix',
            'current_ingress': ingress_val,
            'current_egress': egress_val,
            'current_ingress_mbps': original_ingress_mbps,
            'current_egress_mbps': original_egress_mbps,
            'correct_ingress': correct_ingress,
            'correct_egress': correct_egress,
            'config': config
        }

    except ValueError as e:
        return {
            'container': container_name,
            'status': 'error',
            'reason': f"解析带宽值失败: {e}"
        }

def apply_bandwidth_fix(fix_info):
    """应用带宽修复"""
    container_name = fix_info['container']

    # 备份配置
    backup_file = backup_container_config(container_name, fix_info['config'])
    if not backup_file:
        return False, "备份配置失败"

    print(f"  📁 配置已备份到: {backup_file}")

    # 应用修复
    cmd1 = f"lxc config device set {container_name} eth0 limits.ingress={fix_info['correct_ingress']}"
    cmd2 = f"lxc config device set {container_name} eth0 limits.egress={fix_info['correct_egress']}"

    success1, _, stderr1 = run_command(cmd1)
    if not success1:
        return False, f"设置ingress失败: {stderr1}"

    success2, _, stderr2 = run_command(cmd2)
    if not success2:
        # 回滚ingress设置
        rollback_cmd = f"lxc config device set {container_name} eth0 limits.ingress={fix_info['current_ingress']}"
        run_command(rollback_cmd)
        return False, f"设置egress失败: {stderr2}"

    return True, "修复成功"

def main():
    print("=== 安全的批量修复容器带宽限制脚本 ===")
    print("此脚本将修复由于单位转换错误导致的带宽限制问题")
    print("安全特性：备份配置、逐个确认、详细预览")
    print()

    # 获取所有容器
    containers = get_all_containers()
    if not containers:
        print("没有找到任何容器")
        return

    print(f"找到 {len(containers)} 个容器，正在分析...")
    print()

    # 分析所有容器
    fix_candidates = []
    skip_containers = []
    error_containers = []

    for container in containers:
        ingress, egress, config = get_container_network_limits(container)

        if ingress is None or egress is None:
            skip_containers.append({'container': container, 'reason': '无网络限制配置'})
            continue

        analysis = analyze_container_bandwidth(container, ingress, egress, config)

        if analysis['status'] == 'need_fix':
            fix_candidates.append(analysis)
        elif analysis['status'] == 'skip':
            skip_containers.append(analysis)
        else:
            error_containers.append(analysis)

    # 显示分析结果
    print("=" * 60)
    print("分析结果：")
    print(f"需要修复的容器: {len(fix_candidates)} 个")
    print(f"跳过的容器: {len(skip_containers)} 个")
    print(f"错误的容器: {len(error_containers)} 个")
    print()

    # 显示需要修复的容器详情
    if fix_candidates:
        print("需要修复的容器详情：")
        print("-" * 60)
        for fix_info in fix_candidates:
            print(f"容器: {fix_info['container']}")
            print(f"  当前: {fix_info['current_ingress_mbps']} Mbps (ingress={fix_info['current_ingress']} bit/s)")
            print(f"  当前: {fix_info['current_egress_mbps']} Mbps (egress={fix_info['current_egress']} bit/s)")
            print(f"  修复: {fix_info['current_ingress_mbps']} Mbps (ingress={fix_info['correct_ingress']} bit/s)")
            print(f"  修复: {fix_info['current_egress_mbps']} Mbps (egress={fix_info['correct_egress']} bit/s)")
            print()

    # 显示跳过的容器
    if skip_containers:
        print("跳过的容器：")
        print("-" * 60)
        for skip_info in skip_containers:
            print(f"容器: {skip_info['container']} - {skip_info['reason']}")
        print()

    if not fix_candidates:
        print("没有需要修复的容器。")
        return

    # 询问是否继续
    print("=" * 60)
    response = input(f"确认修复以上 {len(fix_candidates)} 个容器？(y/N): ").strip().lower()
    if response != 'y':
        print("操作已取消")
        return

    # 执行修复
    print()
    print("开始修复...")
    print("=" * 60)

    success_count = 0
    failed_containers = []

    for fix_info in fix_candidates:
        container_name = fix_info['container']
        print(f"正在修复容器: {container_name}")

        success, message = apply_bandwidth_fix(fix_info)

        if success:
            print(f"  ✅ {message}")
            success_count += 1
        else:
            print(f"  ❌ {message}")
            failed_containers.append({'container': container_name, 'error': message})

        print()
        time.sleep(0.5)  # 避免操作过快

    # 显示最终结果
    print("=" * 60)
    print("修复完成！")
    print(f"成功修复: {success_count} 个容器")
    print(f"修复失败: {len(failed_containers)} 个容器")

    if failed_containers:
        print("\n失败的容器：")
        for fail_info in failed_containers:
            print(f"  {fail_info['container']}: {fail_info['error']}")

    if success_count > 0:
        print(f"\n配置备份保存在: ./container_backups/ 目录")
        print("修复的容器无需重启，配置已立即生效。")
        print("如需回滚，请使用备份文件手动恢复。")

def test_mode():
    """测试模式：只分析不修改"""
    print("=== 测试模式：分析容器带宽配置 ===")
    print("此模式只分析配置，不会进行任何修改")
    print()

    containers = get_all_containers()
    if not containers:
        print("没有找到任何容器")
        return

    print(f"找到 {len(containers)} 个容器，正在分析...")
    print()

    need_fix = []
    normal_containers = []

    for container in containers:
        ingress, egress, config = get_container_network_limits(container)

        if ingress is None or egress is None:
            normal_containers.append({'container': container, 'status': '无网络限制'})
            continue

        analysis = analyze_container_bandwidth(container, ingress, egress, config)

        if analysis['status'] == 'need_fix':
            need_fix.append(analysis)
        else:
            normal_containers.append(analysis)

    print("=" * 60)
    print("分析结果：")
    print(f"需要修复: {len(need_fix)} 个容器")
    print(f"正常配置: {len(normal_containers)} 个容器")
    print()

    if need_fix:
        print("需要修复的容器：")
        for fix_info in need_fix:
            print(f"  {fix_info['container']}: {fix_info['current_ingress_mbps']}Mbps -> 修复为正确的bit/s值")

    print(f"\n如需执行修复，请运行: python3 {sys.argv[0]}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_mode()
    else:
        main()
