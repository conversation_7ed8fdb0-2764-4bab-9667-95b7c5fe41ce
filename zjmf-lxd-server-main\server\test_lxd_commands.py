#!/usr/bin/env python3
"""
测试LXD命令兼容性
"""

import subprocess
import json

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def test_lxd_commands():
    print("=== 测试LXD命令兼容性 ===")
    
    # 测试版本
    print("1. 测试LXD版本...")
    success, stdout, stderr = run_command("lxc version")
    if success:
        print(f"   LXD版本: {stdout}")
    else:
        print(f"   获取版本失败: {stderr}")
    
    print()
    
    # 测试容器列表命令
    print("2. 测试容器列表命令...")
    
    commands = [
        "lxc list --format json",
        "lxc list -c n --format csv", 
        "lxc list -c n",
        "lxc list"
    ]
    
    for cmd in commands:
        print(f"   测试: {cmd}")
        success, stdout, stderr = run_command(cmd)
        if success:
            print(f"   ✅ 成功")
            if "json" in cmd:
                try:
                    data = json.loads(stdout)
                    print(f"      找到 {len(data)} 个容器")
                except:
                    print("      JSON解析失败")
            elif "csv" in cmd:
                lines = stdout.strip().split('\n')
                print(f"      CSV输出 {len(lines)} 行")
            else:
                lines = stdout.strip().split('\n')
                print(f"      输出 {len(lines)} 行")
            break
        else:
            print(f"   ❌ 失败: {stderr}")
    
    print()
    
    # 测试单个容器配置命令
    print("3. 测试容器配置命令...")
    
    # 先获取一个容器名
    success, stdout, stderr = run_command("lxc list")
    if success:
        lines = stdout.strip().split('\n')
        container_name = None
        for line in lines:
            if '|' in line and not line.startswith('+') and 'NAME' not in line:
                parts = [p.strip() for p in line.split('|')]
                if len(parts) >= 2 and parts[1]:
                    container_name = parts[1]
                    break
        
        if container_name:
            print(f"   使用容器: {container_name}")
            
            config_commands = [
                f"lxc config show {container_name} --format json",
                f"lxc config show {container_name}"
            ]
            
            for cmd in config_commands:
                print(f"   测试: {cmd}")
                success, stdout, stderr = run_command(cmd)
                if success:
                    print(f"   ✅ 成功")
                    if "json" in cmd:
                        try:
                            data = json.loads(stdout)
                            print("      JSON格式正常")
                        except:
                            print("      JSON解析失败")
                    else:
                        print("      YAML格式输出")
                        # 查找网络限制
                        if "limits.ingress" in stdout:
                            print("      找到网络限制配置")
                        else:
                            print("      未找到网络限制配置")
                    break
                else:
                    print(f"   ❌ 失败: {stderr}")
        else:
            print("   未找到可用的容器")
    else:
        print(f"   获取容器列表失败: {stderr}")

if __name__ == "__main__":
    test_lxd_commands()
