#!/usr/bin/env python3
"""
调试LXD命令格式
"""

import subprocess
import json

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def main():
    print("=== 调试LXD命令格式 ===")
    
    # 测试容器列表命令
    print("1. 测试容器列表命令...")
    
    list_commands = [
        "lxc list --format=json",
        "lxc list --format json", 
        "lxc list -f json",
        "lxc list --format=csv -c n",
        "lxc list --format csv -c n",
        "lxc list -f csv -c n"
    ]
    
    working_list_cmd = None
    for cmd in list_commands:
        print(f"   尝试: {cmd}")
        success, stdout, stderr = run_command(cmd)
        if success:
            print(f"   ✅ 成功!")
            working_list_cmd = cmd
            if "json" in cmd:
                try:
                    containers = json.loads(stdout)
                    print(f"   找到 {len(containers)} 个容器")
                    if containers:
                        print(f"   第一个容器: {containers[0]['name']}")
                except:
                    print("   JSON解析失败")
            break
        else:
            print(f"   ❌ 失败: {stderr}")
    
    if not working_list_cmd:
        print("   所有列表命令都失败了")
        return
    
    print()
    
    # 获取一个容器名用于测试
    success, stdout, stderr = run_command(working_list_cmd)
    if not success:
        print("无法获取容器列表")
        return
    
    container_name = None
    if "json" in working_list_cmd:
        try:
            containers = json.loads(stdout)
            if containers:
                container_name = containers[0]['name']
        except:
            pass
    
    if not container_name:
        # 尝试从表格格式解析
        success, stdout, stderr = run_command("lxc list")
        if success:
            lines = stdout.strip().split('\n')
            for line in lines:
                if '|' in line and not line.startswith('+') and 'NAME' not in line:
                    parts = [p.strip() for p in line.split('|')]
                    if len(parts) >= 2 and parts[1]:
                        container_name = parts[1]
                        break
    
    if not container_name:
        print("无法找到容器名")
        return
    
    print(f"2. 测试容器配置命令 (使用容器: {container_name})...")
    
    config_commands = [
        f"lxc config show {container_name} --format=json",
        f"lxc config show {container_name} --format json",
        f"lxc config show {container_name} -f json",
        f"lxc config show {container_name}"
    ]
    
    for cmd in config_commands:
        print(f"   尝试: {cmd}")
        success, stdout, stderr = run_command(cmd)
        if success:
            print(f"   ✅ 成功!")
            if "json" in cmd:
                try:
                    config = json.loads(stdout)
                    devices = config.get('devices', {})
                    eth0 = devices.get('eth0', {})
                    ingress = eth0.get('limits.ingress')
                    egress = eth0.get('limits.egress')
                    print(f"   网络限制: ingress={ingress}, egress={egress}")
                except Exception as e:
                    print(f"   JSON解析失败: {e}")
            else:
                # YAML格式
                if "limits.ingress" in stdout:
                    print("   找到网络限制配置")
                    for line in stdout.split('\n'):
                        if 'limits.ingress' in line or 'limits.egress' in line:
                            print(f"     {line.strip()}")
                else:
                    print("   未找到网络限制配置")
            break
        else:
            print(f"   ❌ 失败: {stderr}")

if __name__ == "__main__":
    main()
