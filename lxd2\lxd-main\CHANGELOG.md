## 待解决的问题

- LXC模板构建自定义的模板提前初始化好部分内容并发布到自己的镜像仓库中，避免原始模板过于干净导致初始化时间过长，以及支持一些旧版本的系统(centos7，centos8，debian8，debian9)，相关资料[1](https://github.com/lxc/lxc-ci/tree/main/images)、[2](https://github.com/lxc/distrobuilder)、[3](https://cloud.tencent.com/developer/article/2348016?areaId=106001)
- 构建WIN的系统镜像，相关资料[1](https://www.microsoft.com/software-download/windows11), [2](https://discourse.ubuntu.com/t/how-to-install-a-windows-11-vm-using-lxd/28940), [3](https://help.aliyun.com/zh/simple-application-server/use-cases/use-vnc-to-build-guis-on-ubuntu-18-04-and-20-04#21e0b772d7fgc)
- 使得宿主机支持更多的系统，不仅限于ubuntu和debian系做宿主机

## 更新日志

2025.04.22

- 调整CDN轮询顺序为随机顺序，避免单个CDN节点压力过大
- 修复设置IPV6网络的时候，没有利用上cdn进行持久化映射设置
- 提取公共代码，减少重复逻辑，模块化代码方便维护
- 容器相关信息同时写入容器的config的user.description，方便web面板查看

2024.03.23

如果LXD的官方第三方源不可用，自行添加

https://mirror.nju.edu.cn/lxc-images/

https://mirrors.tuna.tsinghua.edu.cn/help/lxc-images/

2024.01.15

- 对无zfs的ubuntu系统增加处理

2023.12.26

- 修复在附加IPV6地址时，未判断容器是否已停止就进行附加的问题，部分宿主机性能拉跨仅等待5秒是不够的

2023.12.21

- 修复部分宿主机本身不存在定时任务机制的问题
- 修复IPV6设置分段设置出现设备已存在的问题

2023.12.21

- 尝试修复网速限制可能部分失效的问题

2023.12.20

- 增加IPV6网络保活的定时任务，避免长期不使用导致V6的ndp广播缓存失效

2023.12.11

- 修复网速限制多线程可能失效的问题，增加max限制

2023.11.28

- 修复宿主机可能绑定多个IPV6地址的情况，可能IPV6地址分别存在两个v6的子网，修正识别为已绑定v6子网的前缀

2023.11.22

- 修复可能检测私网IPV6失灵的情况，完善检测逻辑
- 修复可能宿主机内可能绑定不止一个IPV6地址的情况，只测试地址最长的公网IPV6地址

2023.11.07

- 修复ipv6映射中可能出现的无效安装工具包的问题
- 修复ipv6映射自定义映射方式可能失效的问题
- 默认安装过程中指定lxdbr0的默认DNS配置为*******和*******，并指定v4和v6默认启用dhcp方式分配IP

2023.11.06

- 增加ssh_bash对PAM的处理，避免某些系统默认启用PAM校验导致SSH登不上的情况
- 增加ipv6处理过程中选择是否使用iptables进行映射，默认不使用iptables方式进行映射而使用新增网络设备的方式进行映射

2023.11.01

- 增加IPV6的地址加白时自动设置开机自删除默认路由的操作，避免宿主机重启后路由又变回来又冲突了

2023.10.28

- 适配非永久隧道情况下的IPV6地址分配
- 适配非fe80地址加白情况下的IPV6地址分配

2023.10.26

- 替换原先使用切分方式获取子网前缀的方式，现自动计算CIDR格式下的子网前缀

2023.10.23

- 替换原先使用 iptables 的方式给容器分配IPV6地址的方法，使用lxc设备直接绑定地址的方法解决分配问题

2023.10.20

- 开设出的容器尝试支持kali，archlinux，rockylinux，oralce，fedora系统

2023.10.19

- 一键脚本支持自定义设置CPU限制数量，增加参数设置
- 尝试支持openwrt系统的SSH自动设置
- 重新划分SSH启用和设置密码的脚本，分为bash版本和sh版本
- 增加清华源备份源，确保当官方网站丢失和失联时使用第三方镜像源下载镜像

2023.10.09

- 一键安装默认启用lxd的lxcfs相关配置，使得容器内查询容器信息变更为容器本身的信息而不是宿主机信息
- 如果使用的是国内宿主机开设容器，现已配置自动替换包管理器源为清华源

2023.09.02

- 增加宿主机的证书设置+vnstat环境安装+lxd环境安装的三合一脚本，配合LXD站点

2023.09.01

- 开设的容器不支持自定义的含小数点的硬盘设置，暂时只支持整数设置硬盘，现已修复

2023.08.29

- 更新zfs的安装源，同官网的说明适配
- 适配新的面板后端，新增部分脚本

2023.08.17

- 修复开设的容器alpine系统可能存在的2FA问题
- 修复批量开设不支持自定义是否开启IPV6的问题

2023.08.05

- 修复开设的容器alpine系统重启后SSH丢失的问题

2023.07.24

- ubuntu20无法被检索到，上次更新替换了image的查找方式使用json解析，结果判断出问题了，之前没用jq解析json的时候没这个问题的，已修复
- 开设alpine的时候我搬运的我之前写docker虚拟化开设alpine的时候的脚本设置ssh，没想到配置文件不一样，已修复
- 修复了centos系上部分系统ssh开设的问题，对cloudinit文件进行覆盖修改

2023.07.17

- 修复部分镜像检测存在问题，导致无法运行批量开设容器的BUG
- 自定义开设部分增加jq组件检测，如果不存在则自动下载，避免组件缺失

2023.07.09

- 修复未一键安装时导致的缺失文件的问题，所有配置文件移动到/user/local/bin内进行备份，需要使用时再cp加载

2023.07.03

- 调整支持开设的IPV6的子网大小自动适配，而不是写死/64

2023.06.29

- 规整格式，输出全部支持中英双语

2023.06.21

- 修改默认的资源限制放宽了IOPS的限制以加速容器的批量生成
- 部分脚本修改网络限制的顺序，在初始化阶段不限制网络以减少容器初始化阶段需要的时间
- 修改部分脚本提示

2023.06.20

- 新增支持开设出alpine系统的容器
- 修复部分LXD安装的依赖检测问题

2023.06.14

- 修复IPV6地址绑定后如果重启宿主机会导致绑定丢失的问题
- 增加针对IPV6转发的IPV6地址绑定的守护进程，保证重启后IPV6的映射依然存在

2023.06.13 

- LXD的一键安装脚本增加cloud-init文件的识别，~~修改商家的设置，避免DNS重置~~，删除cloud-init组件
- LXD的一键安装脚本增加了进程数限制解除，以使得支持开设100个以上的容器
- 使用dos2unix转换ssh.sh文件的格式避免部分模板识别不到文件，依赖自修复增加对zfs编译的修复
- 宿主机增加DNS检测的守护进程，避免LXD在开设服务器的过程中因为继承宿主机DNS配置，导致的DNS重置回商家原始设置的问题
- ssh.sh文件增加对DNS的改写，增加谷歌的DNS配置，这样即便宿主机DNS有问题也保证LXC容器自己的DNS不出问题
- 批量开设的脚本增加对下载带宽和上传带宽的限制
- 开设容器时支持自定义容器的系统，注意传入参数为系统名字+版本号，如：debian11、ubuntu20，centos7，注意都是小写字母+数字的组合，具体镜像将自动模糊匹配

2023.06.07 将部分组件的安装移动到ssh.sh文件中，且优化依赖安装的部分，支持依赖自修复

2023.05.28 修复部分宿主机原生的DNS和IPV4网络优先级的问题

2023.05.15 增加CDN加速配置文件下载

2023.05.10 增加屏蔽掉可能会导致滥用的组件列表

2023.04.29 修复zfs加载进入内核失败时轮询检测哪个存储类型可用

2023.04.27 修复了老系统一键安装lxd环境时需要lxd.migrate的问题，优化zfs的判别条件

2023.04.26 修复了debian(仅限x86_64架构)的zfs问题，现在一键安装lxd环境的脚本支持debian和ubuntu了
