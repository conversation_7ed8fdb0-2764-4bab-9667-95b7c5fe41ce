# https://ubuntu.com/tutorials/how-to-install-a-windows-11-vm-using-lxd#1-overview
snap install distrobuilder --classic
# https://www.microsoft.com/software-download/windows11
apt-get install genisoimage libwin-hivex-perl -y
apt-get install wimtools -y
apt-get install virt-viewer -y
# --windows-version
# w11 w10 2k12 2k16 2k19 2k22
# --windows-arch
# amd64 ARM64
sudo distrobuilder repack-windows --windows-version w11 --windows-arch amd64 --debug Win11_23H2_Chinese_x64.iso Win11_23H2_Chinese_x64_fix.iso
lxc init win11 --vm --empty -c limits.cpu=4 -c limits.memory=8GB
lxc config device add win11 install disk source=/root/Win11_23H2_Chinese_x64_fix.iso boot.priority=100
lxc config device add win11 vtpm tpm
lxc start win11

# 10e https://software-download.microsoft.com/download/sg/444969d5-f34g-4e03-ac9d-1f9786c69161/19044.1288.211006-0501.21h2_release_svc_refresh_CLIENTENTERPRISEEVAL_OEMRET_x64FRE_en-us.iso
# 2008 https://archive.org/download/en_windows_server_2008_r2_with_sp1_x64_dvd_617601_202006/en_windows_server_2008_r2_with_sp1_x64_dvd_617601.iso
# 2012 http://care.dlservice.microsoft.com/dl/download/6/2/A/62A76ABB-9990-4EFC-A4FE-C7D698DAEB96/9600.17050.WINBLUE_REFRESH.140317-1640_X64FRE_SERVER_EVAL_EN-US-IR3_SSS_X64FREE_EN-US_DV9.ISO
# 2016 https://software-download.microsoft.com/download/pr/Windows_Server_2016_Datacenter_EVAL_en-us_14393_refresh.ISO
# 2019 https://software-download.microsoft.com/download/pr/17763.737.190906-2324.rs5_release_svc_refresh_SERVER_EVAL_x64FRE_en-us_1.iso
# 2022 https://software-download.microsoft.com/download/sg/20348.169.210806-2348.fe_release_svc_refresh_SERVER_EVAL_x64FRE_en-us.iso
