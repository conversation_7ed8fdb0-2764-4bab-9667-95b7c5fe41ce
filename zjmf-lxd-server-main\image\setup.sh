#!/bin/bash

if [ "$(id -u)" -ne 0 ]; then
   echo "错误：此脚本必须以root用户身份运行。"
   echo "请尝试使用 'sudo bash' 命令来执行。"
   exit 1
fi

check_and_install() {
    if ! command -v $1 &> /dev/null; then
        echo "$1 未安装，正在尝试安装..."
        if command -v apt-get &> /dev/null; then
            apt-get update >/dev/null 2>&1 && apt-get install -y $1 >/dev/null 2>&1
        elif command -v yum &> /dev/null; then
            yum install -y $1 >/dev/null 2>&1
        elif command -v dnf &> /dev/null; then
            dnf install -y $1 >/dev/null 2>&1
        else
            echo "无法确定包管理器，请手动安装 $1 后再运行脚本。"
            exit 1
        fi
        if ! command -v $1 &> /dev/null; then
            echo "安装 $1 失败，请手动安装后重试。"
            exit 1
        fi
    fi
}

check_and_install wget
check_and_install unzip
# 安装网络所需依赖
check_and_install iptables
echo "正在安装 iptables-persistent..."
if command -v apt-get &> /dev/null; then
    # 自动应答iptables-persistent安装中的提示
    export DEBIAN_FRONTEND=noninteractive
    apt-get -y install iptables-persistent >/dev/null 2>&1
fi

clear
echo "============================================="
echo "      欢迎使用 LXC 镜像部署脚本"
echo "============================================="
echo
echo "请选择您的设备架构:"
echo "  1) arm64"
echo "  2) amd64"
echo
read -p "请输入选项 [1-2]: " choice

case $choice in
    1)
        ARCH="arm64"
        URL="https://lsez.site/f/5zi0/lxc_image_backups_20250611_032142.zip"
        FILENAME="lxc_image_backups_arm64.zip"
        ;;
    2)
        ARCH="amd64"
        URL="https://lsez.site/f/z4FK/lxc_image_backups_20250611_031849.zip"
        FILENAME="lxc_image_backups_amd64.zip"
        ;;
    *)
        echo "错误：无效的选项，脚本已退出。"
        exit 1
        ;;
esac

DEST_DIR="/root/lxc_image_backups/"

echo "您已选择: ${ARCH}"
echo
echo "步骤 1/5: 开始下载文件..."
wget --progress=bar:force -O ${FILENAME} ${URL}
if [ $? -ne 0 ]; then
    echo "错误：下载失败，请检查您的网络连接或URL是否正确。"
    exit 1
fi
echo "下载完成。"
echo

echo "步骤 2/5: 创建并解压文件到 ${DEST_DIR}..."
mkdir -p ${DEST_DIR}
unzip -o ${FILENAME} -d ${DEST_DIR}
if [ $? -ne 0 ]; then
    echo "错误：解压失败，文件可能已损坏。"
    rm -f ${FILENAME}
    exit 1
fi
rm -f ${FILENAME}
echo "解压完成。"
echo

echo "步骤 3/5: 下载管理脚本..."
wget -O lxd-helper.sh https://raw.githubusercontent.com/xkatld/LinuxTools/refs/heads/main/shell/lxd-helper.sh
if [ $? -ne 0 ]; then
    echo "错误：下载 lxd-helper.sh 失败。"
    exit 1
fi
echo "下载完成。"
echo

echo "步骤 4/5: 运行管理脚本..."
echo "============================================="
chmod +x lxd-helper.sh
./lxd-helper.sh

echo "步骤 5/5: 配置网络环境..."
# 启用IP转发
echo 1 > /proc/sys/net/ipv4/ip_forward
echo "net.ipv4.ip_forward=1" >> /etc/sysctl.conf
sysctl -p

# 获取网络接口和桥接信息
MAIN_INTERFACE=$(ip route | grep default | head -n1 | awk '{print $5}')
if [ -z "$MAIN_INTERFACE" ]; then
    echo "警告：无法自动检测主网络接口，请在app.ini中手动设置MAIN_INTERFACE"
fi

# 自动检测公网IP
NAT_LISTEN_IP=$(curl -s https://api.ipify.org || wget -qO- https://api.ipify.org)
if [ -z "$NAT_LISTEN_IP" ]; then
    echo "警告：无法自动检测公网IP，请在app.ini中手动设置NAT_LISTEN_IP"
fi

# 检查并配置LXD网络
if lxc network show lxdbr0 >/dev/null 2>&1; then
    echo "配置LXD网络..."
    # 获取网桥子网
    BRIDGE_SUBNET=$(ip addr show lxdbr0 | grep 'inet ' | awk '{print $2}')
    if [ -n "$BRIDGE_SUBNET" ]; then
        # 分割IP和掩码
        IP_PART=$(echo $BRIDGE_SUBNET | cut -d'/' -f1)
        MASK_PART=$(echo $BRIDGE_SUBNET | cut -d'/' -f2)
        # 获取前三个IP段
        IP_PREFIX=$(echo $IP_PART | awk -F. '{print $1"."$2"."$3}')
        SUBNET="${IP_PREFIX}.0/${MASK_PART}"
        
        # 添加NAT和FORWARD规则
        if [ -n "$MAIN_INTERFACE" ]; then
            echo "配置NAT规则: $SUBNET -> $MAIN_INTERFACE"
            iptables -t nat -A POSTROUTING -s $SUBNET -o $MAIN_INTERFACE -j MASQUERADE
            iptables -A FORWARD -i lxdbr0 -o $MAIN_INTERFACE -j ACCEPT
            iptables -A FORWARD -i $MAIN_INTERFACE -o lxdbr0 -m state --state RELATED,ESTABLISHED -j ACCEPT
            
            # 保存iptables规则
            if command -v netfilter-persistent &> /dev/null; then
                netfilter-persistent save
            fi
            
            # 创建启动脚本
            cat > /etc/rc.local << EOF
#!/bin/bash
# LXD容器网络自启动配置脚本

# 开启IP转发
echo 1 > /proc/sys/net/ipv4/ip_forward

# 添加NAT规则
iptables -t nat -C POSTROUTING -s $SUBNET -o $MAIN_INTERFACE -j MASQUERADE >/dev/null 2>&1 || \\
iptables -t nat -A POSTROUTING -s $SUBNET -o $MAIN_INTERFACE -j MASQUERADE

# 添加FORWARD规则
iptables -C FORWARD -i lxdbr0 -o $MAIN_INTERFACE -j ACCEPT >/dev/null 2>&1 || \\
iptables -A FORWARD -i lxdbr0 -o $MAIN_INTERFACE -j ACCEPT

iptables -C FORWARD -i $MAIN_INTERFACE -o lxdbr0 -m state --state RELATED,ESTABLISHED -j ACCEPT >/dev/null 2>&1 || \\
iptables -A FORWARD -i $MAIN_INTERFACE -o lxdbr0 -m state --state RELATED,ESTABLISHED -j ACCEPT

exit 0
EOF
            chmod +x /etc/rc.local
            
            # 创建systemd服务
            cat > /etc/systemd/system/lxd-network-setup.service << EOF
[Unit]
Description=LXD容器网络配置服务
After=network.target lxd.service

[Service]
Type=oneshot
ExecStart=/etc/rc.local
TimeoutSec=0
StandardOutput=journal

[Install]
WantedBy=multi-user.target
EOF
            systemctl daemon-reload
            systemctl enable lxd-network-setup.service
            echo "网络规则已配置并设置为系统启动时自动应用"
        else
            echo "警告：未检测到主网络接口，未配置NAT规则"
        fi
    else
        echo "警告：未检测到lxdbr0的IP地址，未配置NAT规则"
    fi
else
    echo "警告：未检测到lxdbr0网桥，未配置NAT规则"
    echo "请确保LXD已正确安装并配置网桥"
fi

echo "============================================="
echo "所有操作已执行完毕。"
echo "注意：如果您需要手动配置网络，请编辑app.ini文件并运行server目录中的network_setup.py脚本"
