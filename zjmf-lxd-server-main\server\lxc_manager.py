from pylxd import Client as LXDClient
from pylxd.exceptions import LXDAPIException, NotFound
from config_handler import app_config
import logging
import json
import subprocess
import os
import shlex
import random
import time
import datetime

logger = logging.getLogger(__name__)

IPTABLES_RULES_METADATA_FILE = 'iptables_rules.json'

def _load_iptables_rules_metadata():
    try:
        if os.path.exists(IPTABLES_RULES_METADATA_FILE):
            with open(IPTABLES_RULES_METADATA_FILE, 'r') as f:
                return json.load(f)
        return []
    except Exception as e:
        logger.error(f"加载iptables规则元数据失败: {e}")
        return []

def _save_iptables_rules_metadata(rules):
    try:
        with open(IPTABLES_RULES_METADATA_FILE, 'w') as f:
            json.dump(rules, f, indent=4)
    except Exception as e:
        logger.error(f"保存iptables规则元数据失败: {e}")

class LXCManager:
    def __init__(self):
        try:
            self.client = LXDClient()
        except LXDAPIException as e:
            logger.critical(f"无法连接到LXD守护进程: {e}")
            raise RuntimeError(f"无法连接到LXD守护进程: {e}")

    def _get_container_or_error(self, hostname):
        try:
            return self.client.containers.get(hostname)
        except NotFound:
            return None
        except LXDAPIException as e:
            logger.error(f"获取容器 {hostname} 时发生LXD API错误: {e}")
            raise ValueError(f"获取容器时LXD API错误: {e}")

    def _get_container_ip(self, container):
        target_bridge = app_config.network_bridge
        nic_name_on_target_bridge = None
        container_name = container.name
        logger.debug(f"开始为容器 {container_name} 获取IP地址，目标网桥: {target_bridge}")
        for device_name, device_config in container.devices.items():
            if device_config.get('type') == 'nic' and device_config.get('network') == target_bridge:
                nic_name_on_target_bridge = device_name
                logger.debug(f"容器 {container_name} 上找到连接到网桥 {target_bridge} 的接口设备: {nic_name_on_target_bridge}")
                break
        if not nic_name_on_target_bridge:
            logger.warning(f"容器 {container_name} 没有找到连接到网桥 {target_bridge} 的网络接口设备。设备列表: {container.devices}")
            return None
        try:
            state = container.state()
            if state.network and nic_name_on_target_bridge in state.network:
                interface_state = state.network[nic_name_on_target_bridge]
                logger.debug(f"容器 {container_name} 接口 {nic_name_on_target_bridge} 的状态: {interface_state}")
                for addr_info in interface_state.get('addresses', []):
                    if addr_info.get('family') == 'inet' and addr_info.get('scope') == 'global':
                        ip_address = addr_info['address']
                        logger.info(f"为容器 {container_name} 在接口 {nic_name_on_target_bridge} 上找到IP: {ip_address}")
                        return ip_address
                logger.warning(f"容器 {container_name} 在接口 {nic_name_on_target_bridge} 上没有找到inet global IP地址。地址列表: {interface_state.get('addresses')}")
            else:
                logger.warning(f"容器 {container_name} 的网络状态中没有接口 {nic_name_on_target_bridge} 的信息。当前网络状态: {state.network}")
        except LXDAPIException as e:
            logger.error(f"获取容器 {container_name} 网络状态时发生LXD API错误: {e}")
        logger.warning(f"未能为容器 {container_name} 获取IP地址")
        return None

    def _get_user_metadata(self, container, key, default=None):
        return container.config.get(f"user.{key}", default)

    def _set_user_metadata(self, container, key, value):
        container.config[f"user.{key}"] = str(value)
        container.save(wait=True)

    def _run_shell_command_for_iptables(self, command_args):
        full_command = ['sudo', 'iptables'] + command_args
        try:
            logger.debug(f"执行iptables命令: {' '.join(full_command)}")
            process = subprocess.Popen(full_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = process.communicate(timeout=15)
            if process.returncode != 0:
                error_message = stderr.decode('utf-8', errors='ignore').strip()
                logger.error(f"iptables命令执行失败 ({process.returncode}): {error_message}. 命令: {' '.join(full_command)}")
                return False, f"iptables命令执行失败: {error_message}"
            logger.info(f"iptables命令成功执行: {' '.join(full_command)}")
            return True, stdout.decode('utf-8', errors='ignore').strip()
        except subprocess.TimeoutExpired:
            logger.error(f"iptables命令超时: {' '.join(full_command)}")
            return False, "iptables命令执行超时"
        except FileNotFoundError:
            logger.error(f"iptables命令未找到，请检查路径: {full_command[0]}")
            return False, "iptables命令未找到"
        except Exception as e:
            logger.error(f"执行iptables命令时发生异常: {str(e)}. 命令: {' '.join(full_command)}")
            return False, f"执行iptables命令时发生异常: {str(e)}"

    def get_container_info(self, hostname):
        container = self._get_container_or_error(hostname)
        if not container:
            return {'code': 404, 'msg': '容器未找到'}
        try:
            state_before = container.state()
            time.sleep(1)
            state = container.state()
            config = container.config
            cpu_cores = int(config.get('limits.cpu', '1'))
            cpu_usage_before = state_before.cpu.get('usage', 0)
            cpu_usage_after = state.cpu.get('usage', 0)
            cpu_usage_diff_ns = cpu_usage_after - cpu_usage_before
            cpu_percent = 0
            if cpu_cores > 0:
                total_possible_ns = 1_000_000_000 * cpu_cores
                cpu_percent = round((cpu_usage_diff_ns / total_possible_ns) * 100, 2)
            total_ram_mb = 0
            mem_limit = config.get('limits.memory', '0MB')
            if mem_limit.upper().endswith('MB'): total_ram_mb = int(mem_limit[:-2])
            elif mem_limit.upper().endswith('GB'): total_ram_mb = int(mem_limit[:-2]) * 1024
            used_ram_mb = int(state.memory['usage'] / (1024*1024)) if state.memory and 'usage' in state.memory else 0
            total_disk_mb = 0
            root_device = container.devices.get('root', {})
            if 'size' in root_device:
                disk_size = root_device['size']
                if disk_size.upper().endswith('MB'): total_disk_mb = int(disk_size[:-2])
                elif disk_size.upper().endswith('GB'): total_disk_mb = int(disk_size[:-2]) * 1024
            used_disk_mb = int(state.disk['root']['usage'] / (1024*1024)) if state.disk and 'root' in state.disk and 'usage' in state.disk['root'] else 0
            status_map = {'Running': 'running', 'Stopped': 'stop', 'Frozen': 'frozen'}
            lxc_status = status_map.get(state.status, 'unknown')
            flow_limit_gb = int(self._get_user_metadata(container, 'flow_limit_gb', 0))
            bytes_total = 0
            if state.network:
                for nic_name, nic_data in state.network.items():
                    if 'counters' in nic_data:
                        bytes_total += nic_data['counters'].get('bytes_received', 0)
                        bytes_total += nic_data['counters'].get('bytes_sent', 0)
            used_flow_gb = round(bytes_total / (1024*1024*1024), 2)
            
            created_at_val = container.created_at
            if isinstance(created_at_val, datetime.datetime):
                created_at_str = created_at_val.isoformat()
            else:
                created_at_str = str(created_at_val) if created_at_val else None

            # --- NEW LOGIC TO GET IMAGE ALIAS ---
            image_source_alias = 'N/A'
            container_fingerprint = container.config.get('volatile.base_image')
            if container_fingerprint:
                try:
                    image = self.client.images.get(container_fingerprint)
                    if image.aliases:
                        image_source_alias = image.aliases[0]['name']
                    else:
                        image_source_alias = image.properties.get('description', container_fingerprint)
                except NotFound:
                    logger.warning(f"镜像指纹 {container_fingerprint} 未找到 (容器: {hostname})")
                    image_source_alias = container.config.get('image.description', 'N/A')
                except Exception as e:
                    logger.error(f"获取容器 {hostname} 的镜像别名时出错: {e}")
                    image_source_alias = container.config.get('image.description', 'N/A')
            else:
                image_source_alias = container.config.get('image.description', 'N/A')
            # --- END OF NEW LOGIC ---

            data = {
                'Hostname': hostname, 'Status': lxc_status,
                'UsedCPU': cpu_percent,
                'CPUCores': cpu_cores, # Added for lxdserver module
                'TotalRam': total_ram_mb, 'UsedRam': used_ram_mb,
                'TotalDisk': total_disk_mb, 'UsedDisk': used_disk_mb,
                'IP': self._get_container_ip(container) or 'N/A',
                'Bandwidth': flow_limit_gb,
                'UseBandwidth': used_flow_gb, # For lxdserver module
                'UseBandwidth_GB': used_flow_gb, # For new web UI
                'ImageSourceAlias': image_source_alias, # <-- ADDED NEW FIELD
                'raw_lxd_info': {
                    'name': container.name,
                    'status': container.status,
                    'status_code': state.status_code,
                    'type': 'container',
                    'architecture': container.architecture,
                    'ephemeral': container.ephemeral,
                    'created_at': created_at_str,
                    'profiles': container.profiles,
                    'config': container.config,
                    'devices': container.devices,
                    'state': {
                         'cpu': state.cpu,
                         'disk': state.disk,
                         'memory': state.memory,
                         'network': state.network
                    },
                    'description': container.description
                }
            }
            return {'code': 200, 'msg': '获取成功', 'data': data}
        except LXDAPIException as e:
            logger.error(f"LXD API错误 (getinfo) for {hostname}: {e}")
            return {'code': 500, 'msg': f'LXD API错误 (getinfo): {e}'}
        except Exception as e:
            logger.error(f"获取信息时发生内部错误 for {hostname}: {str(e)}", exc_info=True)
            return {'code': 500, 'msg': f'获取信息时发生内部错误: {str(e)}'}

    def get_container_realtime_stats(self, hostname):
        container = self._get_container_or_error(hostname)
        if not container:
            return {'code': 404, 'msg': '容器未找到'}
        if container.status != 'Running':
            return {'code': 400, 'msg': '容器未运行'}
        try:
            state_before = container.state()
            time.sleep(1)
            state_after = container.state()
            cpu_cores = int(container.config.get('limits.cpu', '1'))
            cpu_usage_before = state_before.cpu.get('usage', 0)
            cpu_usage_after = state_after.cpu.get('usage', 0)
            cpu_usage_diff_ns = cpu_usage_after - cpu_usage_before
            cpu_percent = 0
            if cpu_cores > 0:
                total_possible_ns = 1_000_000_000 * cpu_cores
                cpu_percent = round((cpu_usage_diff_ns / total_possible_ns) * 100, 2)
            used_ram_mb = int(state_after.memory['usage'] / (1024*1024)) if state_after.memory and 'usage' in state_after.memory else 0
            used_disk_mb = int(state_after.disk['root']['usage'] / (1024*1024)) if state_after.disk and 'root' in state_after.disk and 'usage' in state_after.disk['root'] else 0
            bytes_rx_before, bytes_tx_before = 0, 0
            if state_before.network:
                for nic_data in state_before.network.values():
                    if 'counters' in nic_data:
                        bytes_rx_before += nic_data['counters'].get('bytes_received', 0)
                        bytes_tx_before += nic_data['counters'].get('bytes_sent', 0)
            bytes_rx_after, bytes_tx_after, bytes_total = 0, 0, 0
            if state_after.network:
                for nic_data in state_after.network.values():
                    if 'counters' in nic_data:
                        rx = nic_data['counters'].get('bytes_received', 0)
                        tx = nic_data['counters'].get('bytes_sent', 0)
                        bytes_rx_after += rx
                        bytes_tx_after += tx
                        bytes_total += rx + tx
            rx_speed_bps = bytes_rx_after - bytes_rx_before
            tx_speed_bps = bytes_tx_after - bytes_tx_before
            used_flow_gb = round(bytes_total / (1024*1024*1024), 2)
            stats = {
                'cpu_usage_percent': max(0, cpu_percent),
                'memory_usage_mb': used_ram_mb,
                'disk_usage_mb': used_disk_mb,
                'network_rx_kbps': round(rx_speed_bps / 1024, 2),
                'network_tx_kbps': round(tx_speed_bps / 1024, 2),
                'total_flow_used_gb': used_flow_gb,
            }
            return {'code': 200, 'msg': '获取成功', 'data': stats}
        except LXDAPIException as e:
            logger.error(f"LXD API错误 (get_container_realtime_stats) for {hostname}: {e}")
            return {'code': 500, 'msg': f'LXD API错误: {e}'}
        except Exception as e:
            logger.error(f"获取实时状态时发生内部错误 for {hostname}: {str(e)}", exc_info=True)
            return {'code': 500, 'msg': f'获取实时状态时发生内部错误: {str(e)}'}

    # === Restored functions from 'server old' for API compatibility ===

    def create_container(self, params):
        hostname = params.get('hostname')
        if self.client.containers.exists(hostname):
            return {'code': 409, 'msg': '容器已存在'}
        image_alias = params.get('system') or app_config.default_image_alias

        container_config_obj = {
            'name': hostname,
            'source': {'type': 'image', 'alias': image_alias},
            'config': {
                'limits.cpu': str(params.get('cpu', '1')),
                'limits.memory': f"{params.get('ram', '128')}MB",
                'security.nesting': 'true',
            },
            'devices': {
                'root': {
                    'path': '/', 'pool': app_config.storage_pool,
                    'size': f"{params.get('disk', '1024')}MB", 'type': 'disk'
                },
                'eth0': {
                    'name': 'eth0', 'network': app_config.network_bridge, 'type': 'nic'
                }
            }
        }

        if params.get('up') and params.get('down'):
            # 修复：正确的Mbps到bit/s转换 (1 Mbps = 1,000,000 bit/s)
            container_config_obj['devices']['eth0']['limits.ingress'] = f"{int(params.get('up'))*1000000}"
            container_config_obj['devices']['eth0']['limits.egress'] = f"{int(params.get('down'))*1000000}"

        container_to_cleanup_on_error = None
        try:
            logger.info(f"开始创建容器 {hostname} 使用配置: {container_config_obj}")
            container = self.client.containers.create(container_config_obj, wait=True)
            container_to_cleanup_on_error = container

            self._set_user_metadata(container, 'nat_acl_limit', params.get('ports', 0))
            self._set_user_metadata(container, 'flow_limit_gb', params.get('bandwidth', 0))
            self._set_user_metadata(container, 'disk_size_mb', params.get('disk', '1024'))
            logger.info(f"容器 {hostname} 配置完成，开始启动...")
            container.start(wait=True)
            logger.info(f"容器 {hostname} 启动成功")

            logger.info(f"容器 {hostname} 已启动，准备设置初始密码...")
            time.sleep(10)

            new_password = params.get('password')
            if not new_password:
                logger.error(f"为容器 {hostname} 设置初始密码失败：未提供密码。")
            else:
                user_for_password = app_config.default_container_user
                try:
                    logger.info(f"为容器 {hostname} 的用户 {user_for_password} 设置初始密码 (使用 bash -c 'echo ... | chpasswd')")
                    escaped_new_password = shlex.quote(new_password)
                    command_to_execute_in_bash = f"echo '{user_for_password}:{escaped_new_password}' | chpasswd"
                    logger.debug(f"在容器内执行命令: bash -c \"{command_to_execute_in_bash}\"")

                    current_status_check = container.state().status
                    if current_status_check.lower() != 'running':
                        logger.error(f"容器 {hostname} 未处于运行状态 (当前状态: {current_status_check})，无法设置初始密码。")
                    else:
                        exit_code, stdout, stderr = container.execute(['bash', '-c', command_to_execute_in_bash])
                        if exit_code == 0:
                            logger.info(f"容器 {hostname} 初始密码使用 bash -c 'echo ... | chpasswd' 设置成功")
                        else:
                            err_msg_stdout = stdout.decode('utf-8', errors='ignore').strip() if stdout else ""
                            err_msg_stderr = stderr.decode('utf-8', errors='ignore').strip() if stderr else ""
                            full_err_msg = []
                            if err_msg_stdout: full_err_msg.append(f"STDOUT: {err_msg_stdout}")
                            if err_msg_stderr: full_err_msg.append(f"STDERR: {err_msg_stderr}")
                            combined_err_msg = "; ".join(full_err_msg) if full_err_msg else "命令执行失败，但未提供具体错误信息"
                            logger.error(f"容器 {hostname} 设置初始密码失败 (exit_code: {exit_code}): {combined_err_msg}")
                except LXDAPIException as e_passwd:
                    logger.error(f"为容器 {hostname} 设置初始密码时发生LXD API错误: {e_passwd}")
                except Exception as e_passwd_generic:
                    logger.error(f"为容器 {hostname} 设置初始密码时发生未知错误: {e_passwd_generic}", exc_info=True)
            try:
                ssh_external_port_min = 10000
                ssh_external_port_max = 65535
                random_ssh_dport = random.randint(ssh_external_port_min, ssh_external_port_max)
                logger.info(f"尝试为容器 {hostname} 自动添加 SSH (端口 22) 的 NAT 规则，使用外部端口 {random_ssh_dport}")

                container_ip_for_nat = None
                nat_add_attempts = 0
                while not container_ip_for_nat and nat_add_attempts < 3:
                    container_ip_for_nat = self._get_container_ip(container)
                    if container_ip_for_nat: break
                    logger.warning(f"为容器 {hostname} 获取IP失败 (尝试 {nat_add_attempts+1}/3)，等待后重试...")
                    time.sleep(5)
                    nat_add_attempts += 1

                if not container_ip_for_nat:
                    logger.error(f"为容器 {hostname} 自动添加 SSH NAT 规则失败：多次尝试后仍无法获取容器IP地址。")
                else:
                    add_ssh_rule_result = self.add_nat_rule_via_iptables(hostname, 'tcp', str(random_ssh_dport), '22')
                    if add_ssh_rule_result.get('code') == 200:
                        logger.info(f"成功为容器 {hostname} 自动添加 SSH NAT 规则: 外部端口 {random_ssh_dport} -> 内部端口 22")
                    elif add_ssh_rule_result.get('code') == 409:
                        logger.warning(f"尝试为容器 {hostname} 自动添加 SSH NAT 规则失败：外部端口 {random_ssh_dport} 已被此容器的其他规则使用。可尝试重新创建或手动添加其他端口。")
                    else:
                        logger.error(f"为容器 {hostname} 自动添加 SSH NAT 规则失败。外部端口: {random_ssh_dport}, 原因: {add_ssh_rule_result.get('msg')}")
            except Exception as e_ssh_nat:
                logger.error(f"为容器 {hostname} 自动添加 SSH NAT 规则时发生异常: {str(e_ssh_nat)}", exc_info=True)

            return {'code': 200, 'msg': '容器创建成功'}
        except (LXDAPIException, Exception) as e:
            error_type_msg = "LXD API错误" if isinstance(e, LXDAPIException) else "内部错误"
            logger.error(f"创建容器 {hostname} 过程中发生{error_type_msg}: {str(e)}", exc_info=True)
            if container_to_cleanup_on_error and self.client.containers.exists(container_to_cleanup_on_error.name):
                 try:
                     logger.info(f"创建过程中发生错误，尝试删除可能已部分创建的容器 {container_to_cleanup_on_error.name}")
                     current_state = container_to_cleanup_on_error.state()
                     if current_state.status and current_state.status.lower() == 'running':
                         container_to_cleanup_on_error.stop(wait=True)
                     container_to_cleanup_on_error.delete(wait=True)
                     logger.info(f"部分创建的容器 {container_to_cleanup_on_error.name} 已删除。")
                 except Exception as e_cleanup:
                     logger.error(f"尝试清理部分创建的容器 {container_to_cleanup_on_error.name} 时失败: {e_cleanup}")
            return {'code': 500, 'msg': f'{error_type_msg} (create): {str(e)}'}

    def add_nat_rule_via_iptables(self, hostname, dtype, dport, sport):
        container = self._get_container_or_error(hostname)
        if not container: return {'code': 404, 'msg': '容器未找到'}

        logger.info(f"为容器 {hostname} 通过LXD proxy设备添加端口转发规则: {dtype} {dport} -> {sport}")

        # 检查规则限制和已有规则
        limit = int(self._get_user_metadata(container, 'nat_acl_limit', 0))
        rules_metadata = _load_iptables_rules_metadata()
        current_host_rules_count = sum(1 for r in rules_metadata if r.get('hostname') == hostname)

        is_ssh_rule = (str(sport) == '22' and dtype.lower() == 'tcp')
        if not is_ssh_rule and limit > 0 and current_host_rules_count >= limit:
            logger.warning(f"容器 {hostname} 已达到端口转发规则数量上限 ({limit}条)")
            return {'code': 403, 'msg': f'已达到端口转发规则数量上限 ({limit}条)'}
        
        # 生成唯一设备名称和规则ID
        device_name = f"nat-{dtype.lower()}-{dport}"
        rule_comment = f'lxd_controller_nat_{hostname}_{dtype.lower()}_{dport}'
        
        # 检查是否已存在相同端口的转发规则
        for rule_meta in rules_metadata:
            if rule_meta.get('hostname') == hostname and \
               rule_meta.get('dtype', '').lower() == dtype.lower() and \
               str(rule_meta.get('dport')) == str(dport):
                logger.warning(f"容器 {hostname} 的端口转发规则 ({dtype} {dport}) 已存在")
                return {'code': 409, 'msg': '此外部端口和协议的端口转发规则已存在'}

        container_ip = self._get_container_ip(container)
        if not container_ip:
            logger.error(f"为容器 {hostname} 添加端口转发规则失败: 无法获取内部IP")
            return {'code': 500, 'msg': '无法获取容器内部IP地址'}
        
        try:
            # 使用LXD proxy设备添加端口转发 - 使用容器的实际IP而不是127.0.0.1
            container.devices[device_name] = {
                'type': 'proxy',
                'listen': f'{dtype.lower()}:0.0.0.0:{dport}',
                'connect': f'{dtype.lower()}:{container_ip}:{sport}'
            }
            container.save(wait=True)
            
            # 保存元数据以兼容现有系统
            new_rule_meta = {
                'hostname': hostname, 
                'dtype': dtype.lower(), 
                'dport': str(dport),
                'sport': str(sport), 
                'container_ip': container_ip, 
                'rule_id': rule_comment
            }
            rules_metadata.append(new_rule_meta)
            _save_iptables_rules_metadata(rules_metadata)
            
            logger.info(f"容器 {hostname} 添加端口转发规则成功 (使用LXD proxy设备)")
            return {'code': 200, 'msg': '端口转发规则添加成功'}
        except Exception as e:
            logger.error(f"为容器 {hostname} 添加端口转发规则时发生异常: {str(e)}", exc_info=True)
            return {'code': 500, 'msg': f'添加端口转发规则失败: {str(e)}'}

    # === End of restored functions ===

    def delete_container(self, hostname):
        container = self._get_container_or_error(hostname)
        if not container: return {'code': 404, 'msg': '容器未找到'}
        try:
            logger.info(f"开始删除容器 {hostname}")

            logger.info(f"删除容器 {hostname} 前，清理其所有 NAT 规则")
            rules_metadata_snapshot = _load_iptables_rules_metadata()
            rules_for_this_host_to_delete = [
                rule for rule in rules_metadata_snapshot if rule.get('hostname') == hostname
            ]

            if not rules_for_this_host_to_delete:
                logger.info(f"容器 {hostname} 没有找到关联的 NAT 规则。")
            else:
                for rule_meta_to_delete in rules_for_this_host_to_delete:
                    logger.info(f"准备删除容器 {hostname} 的iptables规则: {rule_meta_to_delete}")
                    delete_attempt_result = self.delete_nat_rule_via_iptables(
                        hostname,
                        rule_meta_to_delete['dtype'],
                        rule_meta_to_delete['dport'],
                        rule_meta_to_delete['sport'],
                        container_ip_at_creation_time=rule_meta_to_delete.get('container_ip')
                    )
                    if delete_attempt_result.get('code') == 200:
                        logger.info(f"成功删除 NAT 规则: {rule_meta_to_delete} for {hostname}")
                    else:
                        logger.warning(f"删除 NAT 规则 {rule_meta_to_delete} for {hostname} 可能失败. 原因: {delete_attempt_result.get('msg')}")

            if container.status == 'Running':
                logger.info(f"容器 {hostname} 正在运行，先停止...")
                container.stop(wait=True)

            container.delete(wait=True)
            logger.info(f"容器 {hostname} 删除成功")

            return {'code': 200, 'msg': '容器删除成功'}
        except LXDAPIException as e:
            logger.error(f"LXD API错误 (delete container {hostname}): {e}")
            return {'code': 500, 'msg': f'LXD API错误 (delete): {e}'}
        except Exception as e:
            logger.error(f"删除容器 {hostname} 时发生内部错误: {str(e)}", exc_info=True)
            return {'code': 500, 'msg': f'删除容器时发生内部错误: {str(e)}'}

    def _power_action(self, hostname, action):
        container = self._get_container_or_error(hostname)
        if not container: return {'code': 404, 'msg': '容器未找到'}
        try:
            logger.info(f"对容器 {hostname} 执行电源操作: {action}")
            if action == 'start':
                if container.status == 'Running': return {'code': 200, 'msg': '容器已在运行中'}
                container.start(wait=True)
            elif action == 'stop':
                if container.status == 'Stopped': return {'code': 200, 'msg': '容器已停止'}
                container.stop(wait=True)
            elif action == 'restart':
                container.restart(wait=True)
            logger.info(f"容器 {hostname} {action} 操作成功")
            return {'code': 200, 'msg': f'容器{action}操作成功'}
        except LXDAPIException as e:
            logger.error(f"LXD API错误 (power action {action} for {hostname}): {e}")
            return {'code': 500, 'msg': f'LXD API错误 ({action}): {e}'}
        except Exception as e:
            logger.error(f"电源操作 {action} for {hostname} 时发生内部错误: {str(e)}", exc_info=True)
            return {'code': 500, 'msg': f'电源操作时发生内部错误: {str(e)}'}

    def start_container(self, hostname): return self._power_action(hostname, 'start')
    def stop_container(self, hostname): return self._power_action(hostname, 'stop')
    def restart_container(self, hostname): return self._power_action(hostname, 'restart')

    def change_password(self, hostname, new_password):
        container = self._get_container_or_error(hostname)
        if not container:
            return {'code': 404, 'msg': '容器未找到'}

        current_status_check = container.state().status
        if current_status_check.lower() != 'running':
            logger.warning(f"尝试为容器 {hostname} 修改密码，但容器未运行 (状态: {current_status_check})")
            return {'code': 400, 'msg': f'容器未运行 (状态: {current_status_check})'}
        try:
            user = app_config.default_container_user

            # 检测是否为Alpine系统
            is_alpine = False
            try:
                exit_code, stdout, _ = container.execute(['/bin/sh', '-c', 'cat /etc/os-release | grep -i alpine'])
                if exit_code == 0 and stdout:
                    stdout_str = stdout.decode('utf-8', errors='ignore')
                    if 'alpine' in stdout_str.lower():
                        is_alpine = True
                        logger.info(f"检测到容器 {hostname} 为Alpine系统，将使用特定的密码设置方法")
            except Exception as e_detect:
                logger.warning(f"检测容器 {hostname} 系统类型时出错: {e_detect}")

            # 添加备用检测方法
            if not is_alpine:
                try:
                    # 检查是否存在apk命令(Alpine特有)
                    exit_code, _, _ = container.execute(['/bin/sh', '-c', 'which apk'])
                    if exit_code == 0:
                        is_alpine = True
                        logger.info(f"通过apk命令检测到容器 {hostname} 为Alpine系统")
                except Exception as e_detect2:
                    logger.warning(f"备用方法检测容器 {hostname} 系统类型时出错: {e_detect2}")
            
            escaped_new_password = shlex.quote(new_password)
            
            if is_alpine:
                # Alpine特殊处理
                logger.info(f"为Alpine容器 {hostname} 的用户 {user} 设置密码")
                try:
                    # 先安装必要的包
                    logger.info(f"为Alpine容器 {hostname} 安装必要的软件包")
                    container.execute(['/bin/sh', '-c', 'apk update'])
                    # 安装openssh和调试工具
                    container.execute(['/bin/sh', '-c', 'apk add openssh shadow openrc curl busybox-extras'])
                    
                    # 使用完整的SSH配置流程
                    logger.info(f"为Alpine容器 {hostname} 配置并启动SSH服务 (完整流程)")
                    
                    # 创建必要的目录
                    container.execute(['/bin/sh', '-c', 'mkdir -p /var/run/sshd'])
                    
                    # 修改SSH配置允许root登录和密码认证
                    container.execute(['/bin/sh', '-c', 'sed -i "s/#PermitRootLogin.*/PermitRootLogin yes/g" /etc/ssh/sshd_config'])
                    container.execute(['/bin/sh', '-c', 'sed -i "s/#PasswordAuthentication.*/PasswordAuthentication yes/g" /etc/ssh/sshd_config'])
                    container.execute(['/bin/sh', '-c', 'sed -i "s/PasswordAuthentication.*/PasswordAuthentication yes/g" /etc/ssh/sshd_config'])
                    
                    # 生成SSH密钥（如果不存在）
                    container.execute(['/bin/sh', '-c', 'if [ ! -f /etc/ssh/ssh_host_rsa_key ]; then ssh-keygen -A; fi'])
                    
                    # 设置密码
                    command_to_execute = f"echo '{user}:{escaped_new_password}' | chpasswd"
                    exit_code, stdout, stderr = container.execute(['/bin/sh', '-c', command_to_execute])
                    
                    # 添加到启动服务
                    container.execute(['/bin/sh', '-c', 'rc-update add sshd default'])
                    
                    # 直接启动SSH服务
                    logger.info(f"直接启动Alpine容器 {hostname} 的SSH服务")
                    start_ssh_output = container.execute(['/bin/sh', '-c', '/usr/sbin/sshd'])
                    logger.info(f"启动SSH服务结果: {start_ssh_output}")
                    
                    # 验证SSH服务是否已启动
                    verify_cmd, verify_stdout, verify_stderr = container.execute(['/bin/sh', '-c', 'ps | grep sshd'])
                    if verify_stdout:
                        logger.info(f"SSH进程验证: {verify_stdout.decode('utf-8', errors='ignore')}")
                    else:
                        logger.warning(f"未检测到SSH进程! stderr: {verify_stderr.decode('utf-8', errors='ignore')}")

                    # 添加额外的自启动方式（多重保障）
                    container.execute(['/bin/sh', '-c', 'mkdir -p /etc/local.d'])
                    container.execute(['/bin/sh', '-c', 'echo "#!/bin/sh" > /etc/local.d/sshd.start'])
                    container.execute(['/bin/sh', '-c', 'echo "/usr/sbin/sshd" >> /etc/local.d/sshd.start'])
                    container.execute(['/bin/sh', '-c', 'chmod +x /etc/local.d/sshd.start'])
                    container.execute(['/bin/sh', '-c', 'rc-update add local default'])
                    
                    # 检查22端口是否已在监听
                    port_check_cmd, port_stdout, port_stderr = container.execute(['/bin/sh', '-c', 'netstat -tulpn | grep :22'])
                    if port_stdout:
                        logger.info(f"端口22监听状态: {port_stdout.decode('utf-8', errors='ignore')}")
                    else:
                        logger.warning(f"端口22未在监听! stderr: {port_stderr.decode('utf-8', errors='ignore')}")
                        
                    logger.info(f"Alpine容器 {hostname} SSH配置完成")
                    
                except Exception as e_alpine:
                    logger.error(f"为Alpine容器 {hostname} 安装或配置SSH时出错: {e_alpine}")
                    # 不要修改exit_code，确保重装流程继续
                    logger.info(f"尽管SSH配置出现错误，但将继续重装流程以避免上层应用失败: {e_alpine}")
            else:
                # 常规系统处理
                logger.info(f"开始为容器 {hostname} 的用户 {user} 修改密码 (使用 bash -c 'echo ... | chpasswd')")
                command_to_execute_in_bash = f"echo '{user}:{escaped_new_password}' | chpasswd"
                logger.debug(f"在容器内执行命令: bash -c \"{command_to_execute_in_bash}\"")
                exit_code, stdout, stderr = container.execute(['bash', '-c', command_to_execute_in_bash])

            if exit_code == 0:
                logger.info(f"容器 {hostname} 密码修改成功")
                return {'code': 200, 'msg': '密码修改成功'}
            else:
                err_msg_stdout = stdout.decode('utf-8', errors='ignore').strip() if stdout else ""
                err_msg_stderr = stderr.decode('utf-8', errors='ignore').strip() if stderr else ""
                full_err_msg = []
                if err_msg_stdout: full_err_msg.append(f"STDOUT: {err_msg_stdout}")
                if err_msg_stderr: full_err_msg.append(f"STDERR: {err_msg_stderr}")
                combined_err_msg = "; ".join(full_err_msg) if full_err_msg else "命令执行失败，但未提供具体错误信息"
                logger.error(f"容器 {hostname} 密码修改失败 (exit_code: {exit_code}): {combined_err_msg}")
                return {'code': 500, 'msg': f'密码修改失败: {combined_err_msg}'}
        except LXDAPIException as e:
            logger.error(f"LXD API错误 (change password for {hostname}): {e}")
            return {'code': 500, 'msg': f'LXD API错误 (password): {e}'}
        except Exception as e:
            logger.error(f"修改密码 for {hostname} 时发生内部错误: {str(e)}", exc_info=True)
            return {'code': 500, 'msg': f'修改密码时发生内部错误: {str(e)}'}

    def reinstall_container(self, hostname, new_os_alias, new_password):
        container = self._get_container_or_error(hostname)
        if not container: return {'code': 404, 'msg': '容器未找到'}
        logger.info(f"开始重装容器 {hostname} 为系统 {new_os_alias}")

        # 保存所有NAT规则用于重装后恢复
        logger.info(f"保存容器 {hostname} 的所有端口转发规则")
        saved_nat_rules = []
        try:
            rules_result = self.list_nat_rules(hostname)
            if isinstance(rules_result, dict) and 'data' in rules_result:
                saved_nat_rules = rules_result.get('data', [])
                # 确保保存的规则使用了正确的键名格式
                for rule in saved_nat_rules:
                    if 'Dtype' not in rule and 'type' in rule:
                        rule['Dtype'] = rule['type']
                    if 'Dport' not in rule and 'dport' in rule:
                        rule['Dport'] = rule['dport']
                    if 'Sport' not in rule and 'sport' in rule:
                        rule['Sport'] = rule['sport']
                    if 'ID' not in rule and 'id' in rule:
                        rule['ID'] = rule['id']
                logger.info(f"成功保存容器 {hostname} 的 {len(saved_nat_rules)} 条端口转发规则")
                logger.debug(f"保存的规则: {saved_nat_rules}")
            else:
                logger.warning(f"保存容器 {hostname} 的端口转发规则失败: 返回格式异常 {rules_result}")
        except Exception as e:
            logger.error(f"保存容器 {hostname} 的端口转发规则时发生错误: {e}")

        # 保存原先的SSH端口信息用于后续恢复
        original_ssh_port = None
        rules_metadata_snapshot_reinstall = _load_iptables_rules_metadata()
        for rule in rules_metadata_snapshot_reinstall:
            if (rule.get('hostname') == hostname and 
                rule.get('dtype', '').lower() == 'tcp' and 
                str(rule.get('sport')) == '22'):
                original_ssh_port = rule.get('dport')
                logger.info(f"发现容器 {hostname} 原有SSH端口转发: 外部端口 {original_ssh_port} -> 内部端口 22")
                break

        original_config_keys = ['limits.cpu', 'limits.memory', 'user.nat_acl_limit', 'user.flow_limit_gb', 'user.disk_size_mb']
        original_devices_to_keep_config = ['eth0']

        preserved_config = {k: v for k, v in container.config.items() if k in original_config_keys}
        preserved_devices = {dev_name: dev_data for dev_name, dev_data in container.devices.items() if dev_name in original_devices_to_keep_config}
        new_root_size = self._get_user_metadata(container, 'disk_size_mb', '1024') + "MB"

        try:
            if container.status == 'Running':
                logger.info(f"容器 {hostname}正在运行，重装前先停止...")
                container.stop(wait=True)

            logger.info(f"重装容器 {hostname} 前，清理其所有 NAT 规则")
            rules_for_this_host_to_delete_reinstall = [
                rule for rule in rules_metadata_snapshot_reinstall if rule.get('hostname') == hostname
            ]

            if not rules_for_this_host_to_delete_reinstall:
                logger.info(f"容器 {hostname} (重装前) 没有找到关联的 NAT 规则。")
            else:
                for rule_meta_to_delete in rules_for_this_host_to_delete_reinstall:
                    logger.info(f"准备删除容器 {hostname} (重装前) 的iptables规则: {rule_meta_to_delete}")
                    delete_attempt_result = self.delete_nat_rule_via_iptables(
                        hostname,
                        rule_meta_to_delete['dtype'],
                        rule_meta_to_delete['dport'],
                        rule_meta_to_delete['sport'],
                        container_ip_at_creation_time=rule_meta_to_delete.get('container_ip')
                    )
                    if delete_attempt_result.get('code') == 200:
                        logger.info(f"成功删除 NAT 规则 (重装前): {rule_meta_to_delete} for {hostname}")
                    else:
                        logger.warning(f"删除 NAT 规则 (重装前) {rule_meta_to_delete} for {hostname} 可能失败. 原因: {delete_attempt_result.get('msg')}")

            logger.info(f"准备删除旧容器 {hostname}...")
            container.delete(wait=True)
            logger.info(f"旧容器 {hostname} 已删除，开始创建新容器...")

            reinstall_lxd_config = {
                'name': hostname,
                'source': {'type': 'image', 'alias': new_os_alias or app_config.default_image_alias},
                'config': preserved_config,
                'devices': preserved_devices
            }
            reinstall_lxd_config['devices']['root'] = {'path': '/', 'pool': app_config.storage_pool, 'size': new_root_size, 'type': 'disk'}

            new_container = self.client.containers.create(reinstall_lxd_config, wait=True)
            logger.info(f"新容器 {hostname} 配置完成，开始启动...")
            new_container.start(wait=True)
            logger.info(f"容器 {hostname} 重装并启动成功.")

            # 这部分是设置密码和SSH配置，即使失败也不影响重装成功
            ssh_config_success = True  # 标记SSH配置是否成功
            logger.info(f"重装后的容器 {hostname} 已启动，准备设置密码...")
            time.sleep(10)

            if not new_password:
                logger.error(f"为重装后的容器 {hostname} 设置密码失败：未提供密码。")
                ssh_config_success = False
            else:
                user_for_password = app_config.default_container_user
                try:
                    # 检测是否为Alpine系统
                    is_alpine = False
                    try:
                        exit_code, stdout, _ = new_container.execute(['/bin/sh', '-c', 'cat /etc/os-release | grep -i alpine'])
                        if exit_code == 0 and stdout:
                            stdout_str = stdout.decode('utf-8', errors='ignore')
                            if 'alpine' in stdout_str.lower():
                                is_alpine = True
                                logger.info(f"检测到容器 {hostname} 为Alpine系统，将使用特定的密码设置方法")
                    except Exception as e_detect:
                        logger.warning(f"检测容器 {hostname} 系统类型时出错: {e_detect}")
                    
                    # 添加备用检测方法
                    if not is_alpine:
                        try:
                            # 检查是否存在apk命令(Alpine特有)
                            exit_code, _, _ = new_container.execute(['/bin/sh', '-c', 'which apk'])
                            if exit_code == 0:
                                is_alpine = True
                                logger.info(f"通过apk命令检测到容器 {hostname} 为Alpine系统")
                        except Exception as e_detect2:
                            logger.warning(f"备用方法检测容器 {hostname} 系统类型时出错: {e_detect2}")
                    
                    escaped_new_password = shlex.quote(new_password)
                    
                    try:
                        if is_alpine:
                            # Alpine特殊处理
                            logger.info(f"为Alpine容器 {hostname} 的用户 {user_for_password} 设置密码")
                            try:
                                # 先安装必要的包
                                logger.info(f"为Alpine容器 {hostname} 安装必要的软件包")
                                new_container.execute(['/bin/sh', '-c', 'apk update'])
                                # 安装openssh和调试工具
                                new_container.execute(['/bin/sh', '-c', 'apk add openssh shadow openrc curl busybox-extras'])
                                
                                # 使用完整的SSH配置流程
                                logger.info(f"为Alpine容器 {hostname} 配置并启动SSH服务 (完整流程)")
                                
                                # 创建必要的目录
                                new_container.execute(['/bin/sh', '-c', 'mkdir -p /var/run/sshd'])
                                
                                # 修改SSH配置允许root登录和密码认证
                                new_container.execute(['/bin/sh', '-c', 'sed -i "s/#PermitRootLogin.*/PermitRootLogin yes/g" /etc/ssh/sshd_config'])
                                new_container.execute(['/bin/sh', '-c', 'sed -i "s/#PasswordAuthentication.*/PasswordAuthentication yes/g" /etc/ssh/sshd_config'])
                                new_container.execute(['/bin/sh', '-c', 'sed -i "s/PasswordAuthentication.*/PasswordAuthentication yes/g" /etc/ssh/sshd_config'])
                                
                                # 生成SSH密钥（如果不存在）
                                new_container.execute(['/bin/sh', '-c', 'if [ ! -f /etc/ssh/ssh_host_rsa_key ]; then ssh-keygen -A; fi'])
                                
                                # 设置密码
                                command_to_execute = f"echo '{user_for_password}:{escaped_new_password}' | chpasswd"
                                exit_code, stdout, stderr = new_container.execute(['/bin/sh', '-c', command_to_execute])
                                
                                # 添加到启动服务
                                new_container.execute(['/bin/sh', '-c', 'rc-update add sshd default'])
                                
                                # 直接启动SSH服务
                                logger.info(f"直接启动Alpine容器 {hostname} 的SSH服务")
                                start_ssh_output = new_container.execute(['/bin/sh', '-c', '/usr/sbin/sshd'])
                                logger.info(f"启动SSH服务结果: {start_ssh_output}")
                                
                                # 验证SSH服务是否已启动
                                verify_cmd, verify_stdout, verify_stderr = new_container.execute(['/bin/sh', '-c', 'ps | grep sshd'])
                                if verify_stdout:
                                    logger.info(f"SSH进程验证: {verify_stdout.decode('utf-8', errors='ignore')}")
                                else:
                                    logger.warning(f"未检测到SSH进程! stderr: {verify_stderr.decode('utf-8', errors='ignore')}")
                                    ssh_config_success = False

                                # 添加额外的自启动方式（多重保障）
                                new_container.execute(['/bin/sh', '-c', 'mkdir -p /etc/local.d'])
                                new_container.execute(['/bin/sh', '-c', 'echo "#!/bin/sh" > /etc/local.d/sshd.start'])
                                new_container.execute(['/bin/sh', '-c', 'echo "/usr/sbin/sshd" >> /etc/local.d/sshd.start'])
                                new_container.execute(['/bin/sh', '-c', 'chmod +x /etc/local.d/sshd.start'])
                                new_container.execute(['/bin/sh', '-c', 'rc-update add local default'])
                                
                                # 检查22端口是否已在监听
                                port_check_cmd, port_stdout, port_stderr = new_container.execute(['/bin/sh', '-c', 'netstat -tulpn | grep :22'])
                                if port_stdout:
                                    logger.info(f"端口22监听状态: {port_stdout.decode('utf-8', errors='ignore')}")
                                else:
                                    logger.warning(f"端口22未在监听! stderr: {port_stderr.decode('utf-8', errors='ignore')}")
                                    ssh_config_success = False
                                    
                                logger.info(f"Alpine容器 {hostname} SSH配置完成")
                                
                            except Exception as e_alpine:
                                logger.error(f"为Alpine容器 {hostname} 安装或配置SSH时出错: {e_alpine}")
                                ssh_config_success = False
                                logger.info(f"尽管SSH配置出现错误，但将继续重装流程以避免上层应用失败: {e_alpine}")
                        else:
                            # 常规系统处理
                            logger.info(f"为常规容器 {hostname} 的用户 {user_for_password} 设置密码 (使用 bash -c 'echo ... | chpasswd')")
                            command_to_execute_in_bash = f"echo '{user_for_password}:{escaped_new_password}' | chpasswd"
                            exit_code, stdout, stderr = new_container.execute(['bash', '-c', command_to_execute_in_bash])
                        
                        if exit_code == 0:
                            logger.info(f"重装后的容器 {hostname} 密码设置成功")
                        else:
                            err_msg_stdout = stdout.decode('utf-8', errors='ignore').strip() if stdout else ""
                            err_msg_stderr = stderr.decode('utf-8', errors='ignore').strip() if stderr else ""
                            full_err_msg = []
                            if err_msg_stdout: full_err_msg.append(f"STDOUT: {err_msg_stdout}")
                            if err_msg_stderr: full_err_msg.append(f"STDERR: {err_msg_stderr}")
                            combined_err_msg = "; ".join(full_err_msg) if full_err_msg else "命令执行失败，但未提供具体错误信息"
                            logger.error(f"重装后的容器 {hostname} 设置密码失败 (exit_code: {exit_code}): {combined_err_msg}")
                            ssh_config_success = False
                    except Exception as e_ssh:
                        logger.error(f"为容器 {hostname} 配置SSH时发生异常: {e_ssh}")
                        ssh_config_success = False
                except LXDAPIException as e_passwd:
                    logger.error(f"为重装后的容器 {hostname} 设置密码时发生LXD API错误: {e_passwd}")
                    ssh_config_success = False
                except Exception as e_passwd_generic:
                    logger.error(f"为重装后的容器 {hostname} 设置密码时发生未知错误: {e_passwd_generic}", exc_info=True)
                    ssh_config_success = False

            # NAT规则添加也不影响重装成功
            nat_config_success = True
            try:
                # 先等待IP地址分配
                container_ip_for_nat_reinstall = None
                nat_add_attempts_reinstall = 0
                while not container_ip_for_nat_reinstall and nat_add_attempts_reinstall < 3:
                    container_ip_for_nat_reinstall = self._get_container_ip(new_container)
                    if container_ip_for_nat_reinstall: break
                    logger.warning(f"为重装后的容器 {hostname} 获取IP失败 (尝试 {nat_add_attempts_reinstall+1}/3)，等待后重试...")
                    time.sleep(5)
                    nat_add_attempts_reinstall += 1
                
                if not container_ip_for_nat_reinstall:
                    logger.error(f"为重装后的容器 {hostname} 恢复端口转发规则失败：多次尝试后仍无法获取容器IP地址。")
                    nat_config_success = False
                else:
                    # 1. 确保SSH端口转发存在（优先级高）
                    if original_ssh_port:
                        ssh_port_to_use = original_ssh_port
                        logger.info(f"使用原始SSH端口 {ssh_port_to_use} 为容器 {hostname} 添加NAT规则")
                    else:
                        ssh_external_port_min_reinstall = 10000
                        ssh_external_port_max_reinstall = 65535
                        ssh_port_to_use = random.randint(ssh_external_port_min_reinstall, ssh_external_port_max_reinstall)
                        logger.info(f"没有找到原始SSH端口，使用随机端口 {ssh_port_to_use} 为容器 {hostname} 添加NAT规则")
                    
                    logger.info(f"尝试为重装后的容器 {hostname} 自动添加 SSH (端口 22) 的 NAT 规则，使用外部端口 {ssh_port_to_use}")
                    add_ssh_rule_result_reinstall = self.add_nat_rule_via_iptables(new_container.name, 'tcp', str(ssh_port_to_use), '22')
                    if add_ssh_rule_result_reinstall.get('code') == 200:
                        logger.info(f"成功为重装后的容器 {hostname} 自动添加 SSH NAT 规则: 外部端口 {ssh_port_to_use} -> 内部端口 22")
                    elif add_ssh_rule_result_reinstall.get('code') == 409:
                        logger.warning(f"尝试为重装后的容器 {hostname} 自动添加 SSH NAT 规则失败：外部端口 {ssh_port_to_use} 已被此容器的其他规则使用。可尝试手动添加其他端口。")
                    else:
                        logger.error(f"为重装后的容器 {hostname} 自动添加 SSH NAT 规则失败。外部端口: {ssh_port_to_use}, 原因: {add_ssh_rule_result_reinstall.get('msg')}")
                        
                    # 2. 恢复先前保存的所有端口转发规则
                    restored_count = 0
                    failed_count = 0
                    
                    # 排除SSH规则以避免重复添加
                    saved_non_ssh_nat_rules = []
                    for rule in saved_nat_rules:
                        # 跳过SSH规则，因为我们已经添加了
                        if rule.get('Dtype', '').lower() == 'tcp' and str(rule.get('Sport')) == '22':
                            continue
                        saved_non_ssh_nat_rules.append(rule)
                    
                    logger.info(f"开始为容器 {hostname} 恢复 {len(saved_non_ssh_nat_rules)} 条非SSH端口转发规则")
                    
                    for rule in saved_non_ssh_nat_rules:
                        rule_type = rule.get('Dtype', '').lower()  # 注意这里的键名大写开头
                        dport = rule.get('Dport')                  # 注意这里的键名大写开头
                        sport = rule.get('Sport')                  # 注意这里的键名大写开头
                        
                        if not all([rule_type, dport, sport]):
                            logger.warning(f"跳过无效的端口转发规则: {rule}")
                            failed_count += 1
                            continue
                        
                        logger.info(f"恢复端口转发规则: {rule_type} {dport} -> {sport}")
                        result = self.add_nat_rule_via_iptables(hostname, rule_type, str(dport), str(sport))
                        
                        if result.get('code') == 200:
                            logger.info(f"成功恢复端口转发规则: {rule_type} {dport} -> {sport}")
                            restored_count += 1
                        else:
                            logger.warning(f"恢复端口转发规则失败: {rule_type} {dport} -> {sport}, 原因: {result.get('msg')}")
                            failed_count += 1
                    
                    logger.info(f"容器 {hostname} 的端口转发规则恢复完成: 成功 {restored_count}, 失败 {failed_count}")
                    
                    if failed_count > 0:
                        nat_config_success = False
            except Exception as e_nat_reinstall:
                logger.error(f"为重装后的容器 {hostname} 恢复端口转发规则时发生异常: {str(e_nat_reinstall)}", exc_info=True)
                nat_config_success = False

            # 重装成功但SSH或NAT可能有问题
            if not ssh_config_success:
                logger.warning(f"容器 {hostname} 重装成功，但SSH配置可能有问题")
            if not nat_config_success:
                logger.warning(f"容器 {hostname} 重装成功，但部分端口转发规则可能没有成功恢复")
                
            # 不管SSH或NAT是否配置成功，只要容器创建和启动成功，就返回成功状态
            return {'code': 200, 'msg': '系统重装成功'}
            
        except LXDAPIException as e:
            logger.error(f"LXD API错误 (reinstall {hostname}): {e}")
            return {'code': 500, 'msg': f'LXD API错误 (reinstall): {e}'}
        except Exception as e:
            logger.error(f"重装容器 {hostname} 时发生内部错误: {str(e)}", exc_info=True)
            return {'code': 500, 'msg': f'重装容器时发生内部错误: {str(e)}'}

    def list_nat_rules(self, hostname):
        logger.debug(f"列出容器 {hostname} 的NAT规则")
        
        # 检查容器是否存在
        container = self._get_container_or_error(hostname)
        if not container:
            logger.warning(f"获取NAT规则失败: 容器 {hostname} 不存在")
            return {'code': 404, 'msg': '容器未找到', 'data': []}
        
        # 从元数据中读取规则
        rules_metadata = _load_iptables_rules_metadata()
        container_rules = []
        
        # 遍历元数据中的规则
        for rule_meta in rules_metadata:
            if rule_meta.get('hostname') == hostname:
                container_rules.append({
                    'Dtype': rule_meta.get('dtype','').upper(),  # 使用大写开头的键名
                    'Dport': rule_meta.get('dport'),            # 使用大写开头的键名
                    'Sport': rule_meta.get('sport'),            # 使用大写开头的键名
                    'ID': rule_meta.get('rule_id', f"iptables-{rule_meta.get('dtype')}-{rule_meta.get('dport')}")  # 使用大写开头的键名
                })
        
        # 同时检查容器设备是否有proxy设备（用于兼容性检查）
        for device_name, device_info in container.devices.items():
            if device_info.get('type') == 'proxy' and device_name.startswith('nat-'):
                try:
                    listen_parts = device_info.get('listen', '').split(':')
                    connect_parts = device_info.get('connect', '').split(':')
                    
                    if len(listen_parts) == 3 and len(connect_parts) == 3:
                        dtype = listen_parts[0].lower()
                        dport = listen_parts[2]
                        sport = connect_parts[2]
                        
                        # 检查是否已在元数据中
                        rule_exists = False
                        for rule in container_rules:
                            if (rule.get('Dtype', '').lower() == dtype and  # 注意这里使用大写键名
                                str(rule.get('Dport')) == str(dport) and
                                str(rule.get('Sport')) == str(sport)):
                                rule_exists = True
                                break
                        
                        # 如果不存在，添加到列表
                        if not rule_exists:
                            logger.info(f"在容器 {hostname} 的设备中发现未在元数据中的proxy设备: {device_name}")
                            container_rules.append({
                                'Dtype': dtype.upper(),  # 使用大写开头的键名
                                'Dport': dport,          # 使用大写开头的键名
                                'Sport': sport,          # 使用大写开头的键名
                                'ID': f"proxy-{dtype}-{dport}"  # 使用大写开头的键名
                            })
                except Exception as e:
                    logger.warning(f"解析容器 {hostname} 的proxy设备 {device_name} 时发生错误: {str(e)}")
        
        logger.info(f"容器 {hostname} NAT规则列表: {container_rules}")
        return {'code': 200, 'msg': '获取成功', 'data': container_rules}

    def delete_nat_rule_via_iptables(self, hostname, dtype, dport, sport, container_ip_at_creation_time=None):
        logger.info(f"为容器 {hostname} 删除端口转发规则: {dtype} {dport} -> {sport}")
        
        # 加载元数据
        rules_metadata = _load_iptables_rules_metadata()
        rule_comment_to_use = f'lxd_controller_nat_{hostname}_{dtype.lower()}_{dport}'
        device_name = f"nat-{dtype.lower()}-{dport}"
        
        # 找到要删除的规则元数据
        rule_to_delete_meta = None
        for idx, rule_meta_item in enumerate(rules_metadata):
            if (rule_meta_item.get('hostname') == hostname and 
                rule_meta_item.get('dtype', '').lower() == dtype.lower() and 
                str(rule_meta_item.get('dport')) == str(dport) and 
                str(rule_meta_item.get('sport')) == str(sport)):
                rule_to_delete_meta = rule_meta_item
                rule_comment_to_use = rule_meta_item.get('rule_id', rule_comment_to_use)
                break

        # 尝试获取容器
        container = None
        try:
            container = self._get_container_or_error(hostname)
        except Exception as e:
            logger.warning(f"获取容器 {hostname} 失败，可能已被删除: {str(e)}")
        
        # 如果容器存在，删除proxy设备
        if container:
            try:
                if device_name in container.devices:
                    del container.devices[device_name]
                    container.save(wait=True)
                    logger.info(f"成功从容器 {hostname} 中删除proxy设备 {device_name}")
                else:
                    logger.warning(f"未找到容器 {hostname} 中的proxy设备 {device_name}")
            except Exception as e:
                logger.error(f"从容器 {hostname} 删除proxy设备 {device_name} 时发生异常: {str(e)}", exc_info=True)
        
        # 清理元数据
        modified = False
        final_rules = []
        for rule in rules_metadata:
            if (rule.get('hostname') == hostname and 
                rule.get('dtype', '').lower() == dtype.lower() and 
                str(rule.get('dport')) == str(dport) and 
                str(rule.get('sport')) == str(sport)):
                modified = True
                continue
            final_rules.append(rule)
        
        # 保存更新后的元数据
        if modified:
            _save_iptables_rules_metadata(final_rules)
            logger.info(f"成功从元数据中删除规则: {hostname} {dtype} {dport} -> {sport}")
        else:
            logger.warning(f"在元数据中未找到要删除的规则: {hostname} {dtype} {dport} -> {sport}")
        
        return {'code': 200, 'msg': '端口转发规则删除成功'}