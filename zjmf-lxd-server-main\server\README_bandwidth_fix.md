# 容器带宽限制修复脚本使用说明

## 问题描述

zjmf-lxd-server项目中存在带宽限制单位转换错误：
- 错误：使用125000作为Mbps到bit/s的转换系数
- 正确：应该使用1000000作为转换系数
- 结果：配置1000Mbps的容器实际只能达到125Mbps

## 脚本安全特性

1. **只修复明确错误的配置**：只修复使用125000倍数的配置
2. **详细预览**：修复前显示所有将要修改的内容
3. **配置备份**：自动备份每个容器的原始配置
4. **安全检查**：验证计算出的带宽值是否合理(1-10000 Mbps)
5. **逐步确认**：需要用户明确确认才执行修复
6. **错误处理**：修复失败时自动回滚

## 使用步骤

### 1. 测试模式（推荐先运行）

```bash
cd zjmf-lxd-server-main/server
python3 fix_bandwidth_limits.py --test
```

测试模式只分析配置，不会进行任何修改，用于：
- 查看有多少容器需要修复
- 确认脚本能正确识别问题容器
- 验证计算结果是否正确

### 2. 执行修复

```bash
python3 fix_bandwidth_limits.py
```

脚本会：
1. 分析所有容器的网络配置
2. 显示需要修复的容器详情
3. 要求用户确认后才执行修复
4. 自动备份配置并应用修复

## 修复示例

```
容器: test-container
  当前: 1000 Mbps (ingress=125000000 bit/s)  ← 错误配置
  当前: 1000 Mbps (egress=125000000 bit/s)   ← 错误配置
  修复: 1000 Mbps (ingress=1000000000 bit/s) ← 正确配置
  修复: 1000 Mbps (egress=1000000000 bit/s)  ← 正确配置
```

## 安全检查机制

脚本只会修复满足以下条件的容器：
1. `limits.ingress` 和 `limits.egress` 都是125000的倍数
2. 计算出的Mbps值在1-10000范围内
3. 容器存在且可访问
4. 能够成功备份配置

## 备份和回滚

- 配置备份保存在 `container_backups/` 目录
- 备份文件命名格式：`容器名_时间戳.json`
- 如需回滚，可使用备份文件手动恢复

手动回滚示例：
```bash
# 查看备份文件
ls container_backups/

# 使用lxc命令恢复（需要手动操作）
lxc config device set 容器名 eth0 limits.ingress=原始值
lxc config device set 容器名 eth0 limits.egress=原始值
```

## 注意事项

1. **运行权限**：需要root权限或lxd组权限
2. **备份空间**：确保有足够磁盘空间保存备份
3. **网络影响**：修复过程中容器网络不会中断
4. **立即生效**：修复后配置立即生效，无需重启容器

## 故障排除

### 常见错误

1. **权限不足**
   ```
   解决：sudo python3 fix_bandwidth_limits.py
   ```

2. **LXD服务未运行**
   ```
   解决：sudo systemctl start lxd
   ```

3. **容器不存在**
   ```
   脚本会自动跳过不存在的容器
   ```

### 验证修复结果

```bash
# 查看容器网络配置
lxc config show 容器名 | grep limits

# 测试网络速度
lxc exec 容器名 -- wget -O /dev/null http://speedtest.example.com/file
```

## 脚本输出说明

- ✅ 修复成功
- ❌ 修复失败  
- ⚠️ 跳过处理
- 📁 配置已备份

修复完成后会显示详细统计信息和后续建议。
